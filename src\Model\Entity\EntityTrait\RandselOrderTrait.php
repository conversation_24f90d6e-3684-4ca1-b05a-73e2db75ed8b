<?php

namespace App\Model\Entity\EntityTrait;

use App\Enums\EntityFields\ERandselOrder;
use App\Enums\Options\ERandselOrderApprovalType;
use App\Utility\Encryptor;
use Cake\I18n\FrozenTime;

trait RandselOrderTrait
{

    public function changeStatus($status, ERandselOrderApprovalType $randselOrderApprovalType): static
    {
//        debug($status);
        $this->status = $status;
        $this->approval_type = $randselOrderApprovalType->value;
        $this->status_modified = FrozenTime::now();
        return $this;
    }

    /**
     * @return array
     */
    public function responseData(): array
    {
        $response = $this->toArray();

        // general_userが存在する場合は、関連付けされたデータを使用
        if (!empty($response['general_user']) && !empty($response['general_user']['user_profile'])) {
            $response[ERandselOrder::NAME1->value] = $response['general_user']['user_profile']['decrypted_last_name'];
            $response[ERandselOrder::NAME2->value] = $response['general_user']['user_profile']['decrypted_first_name'];
            $response[ERandselOrder::NAME1_HURIGANA->value] = $response['general_user']['user_profile']['decrypted_last_name_kana'];
            $response[ERandselOrder::NAME2_HURIGANA->value] = $response['general_user']['user_profile']['decrypted_first_name_kana'];
            $response[ERandselOrder::ZIP_CODE->value] = $response['general_user']['user_profile']['decrypted_zip_code'];
            $response[ERandselOrder::TDFK_CD->value] = $response['general_user']['user_profile']['decrypted_prefecture_code'];
            $response[ERandselOrder::ADDRESS1->value] = $response['general_user']['user_profile']['decrypted_address1'];
            $response[ERandselOrder::ADDRESS2->value] = $response['general_user']['user_profile']['decrypted_address2'];
            $response[ERandselOrder::ADDRESS3->value] = $response['general_user']['user_profile']['decrypted_address3'];
            $response[ERandselOrder::TEL->value] = $response['general_user']['user_profile']['decrypted_tel'];
            $response[ERandselOrder::EMAIL->value] = $response['general_user']['email'];
            $response[ERandselOrder::EMAIL_SEND_NG_FLG->value] = $response['general_user']['user_profile']['email_send_ng_flg'];

            // user_surveyが存在する場合は、survey_jsonを生成
            if (!empty($response['general_user']['user_survey'])) {
                $response[ERandselOrder::SURVEY_JSON->value] = $this->generateSurveyJson($response['general_user']['user_survey']);
            }

        } else {
            // general_userが存在しない場合は、暗号化されたフィールドから復号化して使用
            $decrypted = $this->decryptDataArray();
            $response[ERandselOrder::NAME1->value] = $decrypted[ERandselOrder::NAME1->value];
            $response[ERandselOrder::NAME2->value] = $decrypted[ERandselOrder::NAME2->value];
            $response[ERandselOrder::NAME1_HURIGANA->value] = $decrypted[ERandselOrder::NAME1_HURIGANA->value];
            $response[ERandselOrder::NAME2_HURIGANA->value] = $decrypted[ERandselOrder::NAME2_HURIGANA->value];
            $response[ERandselOrder::ZIP_CODE->value] = $decrypted[ERandselOrder::ZIP_CODE->value];
            $response[ERandselOrder::TDFK_CD->value] = $decrypted[ERandselOrder::TDFK_CD->value];
            $response[ERandselOrder::ADDRESS1->value] = $decrypted[ERandselOrder::ADDRESS1->value];
            $response[ERandselOrder::ADDRESS2->value] = $decrypted[ERandselOrder::ADDRESS2->value];
            $response[ERandselOrder::ADDRESS3->value] = $decrypted[ERandselOrder::ADDRESS3->value];
            $response[ERandselOrder::TEL->value] = $decrypted[ERandselOrder::TEL->value];
            $response[ERandselOrder::EMAIL->value] = $decrypted[ERandselOrder::EMAIL->value];
            $response[ERandselOrder::EMAIL_SEND_NG_FLG->value] = $response[ERandselOrder::EMAIL_SEND_NG_FLG->value] ?? null;

            // 暗号化されたsurvey_jsonを復号化して使用
            $response[ERandselOrder::SURVEY_JSON->value] = $decrypted[ERandselOrder::SURVEY_JSON->value];
        }

        return $response;
    }

    /**
     * user_surveyデータからsurvey_json形式を生成
     *
     * @param array $userSurvey
     * @return string
     */
    private function generateSurveyJson(array $userSurvey): string
    {
        $questions = [];

        // お子さまの性別
        $childSexValue = '';
        if (!empty($userSurvey['child_sex'])) {
            $childSexValue = match ($userSurvey['child_sex']) {
                1 => '男の子',
                2 => '女の子',
                3 => 'その他',
                default => ''
            };
        }
        $questions[] = ['k' => 'お子さまの性別', 'v' => $childSexValue];

        // お子さまの生年月日（現在は空文字）
        $questions[] = ['k' => 'お子さまの生年月日', 'v' => ''];

        // ご予算
        $budgetValue = '';
        if (!empty($userSurvey['budget'])) {
            $budgetValue = match ($userSurvey['budget']) {
                1 => '1万円未満～3万円',
                2 => '3万円～7万円',
                3 => '7万円～10万円',
                4 => '10万円～30万円',
                5 => '30万円以上',
                default => ''
            };
        }
        $questions[] = ['k' => 'ご予算', 'v' => $budgetValue];

        // カタログ請求のきっかけ（複数回答可）
        $triggers = [];
        if (!empty($userSurvey['question_1_1'])) $triggers[] = 'SNS';
        if (!empty($userSurvey['question_1_2'])) $triggers[] = 'WEB広告';
        if (!empty($userSurvey['question_1_3'])) $triggers[] = '家族の紹介';
        if (!empty($userSurvey['question_1_4'])) $triggers[] = '知人の紹介';
        $questions[] = ['k' => 'カタログ請求のきっかけ（複数回答可）', 'v' => $triggers];

        // 特に重視するポイント（複数回答可）
        $importantPoints = [];
        if (!empty($userSurvey['question_2_1'])) $importantPoints[] = '耐久性';
        if (!empty($userSurvey['question_2_2'])) $importantPoints[] = '色';
        if (!empty($userSurvey['question_2_3'])) $importantPoints[] = 'デザイン';
        if (!empty($userSurvey['question_2_4'])) $importantPoints[] = '機能性';
        if (!empty($userSurvey['question_2_5'])) $importantPoints[] = 'キャラクターコラボ';
        if (!empty($userSurvey['question_2_6'])) $importantPoints[] = '軽量性';
        if (!empty($userSurvey['question_2_7'])) $importantPoints[] = '安全性';
        if (!empty($userSurvey['question_2_8'])) $importantPoints[] = 'ブランド';
        if (!empty($userSurvey['question_2_9'])) $importantPoints[] = 'メーカーの信頼性';
        if (!empty($userSurvey['question_2_10'])) $importantPoints[] = '価格';
        if (!empty($userSurvey['question_2_11'])) $importantPoints[] = 'カスタマイズ性';
        $questions[] = ['k' => '特に重視するポイント（複数回答可）', 'v' => $importantPoints];

        return json_encode(['questions' => $questions], JSON_UNESCAPED_UNICODE);
    }

    public function decryptDataArray(): array
    {
        $decrypted = [];
        foreach ($this->toArray() as $key => $value) {
            $decrypted[$key] = match ($key) {
//                ERandselOrder::ID->value,
//                ERandselOrder::MAKER_ID->value,
//                ERandselOrder::MEMBER_ID->value,
//                ERandselOrder::PRODUCT_ID->value,
//                ERandselOrder::PRODUCT_NAME->value,
//                ERandselOrder::PRICE->value,
//                ERandselOrder::STATUS->value,
//                ERandselOrder::STATUS_MODIFIED->value,
//                ERandselOrder::APPROVAL_TYPE->value,
//                ERandselOrder::IS_CONFIRMED->value,
//                ERandselOrder::CONFIRMED->value,
                ERandselOrder::NAME1->value,
                ERandselOrder::NAME2->value,
                ERandselOrder::NAME1_HURIGANA->value,
                ERandselOrder::NAME2_HURIGANA->value,
                ERandselOrder::ZIP_CODE->value,
                ERandselOrder::TDFK_CD->value,
                ERandselOrder::ADDRESS1->value,
                ERandselOrder::ADDRESS2->value,
                ERandselOrder::ADDRESS3->value,
                ERandselOrder::TEL->value,
                ERandselOrder::EMAIL->value,
//                ERandselOrder::EMAIL_SEND_NG_FLG->value,
                ERandselOrder::SURVEY_JSON->value,
//                ERandselOrder::CREATED->value,
//                ERandselOrder::MODIFIED->value,
                => $value ? Encryptor::decrypt($value) : null,
                default => $value,
            };
        }
        return $decrypted;
    }

    static public function dataEncrypt(array $rawData): array
    {
        $encrypted = [];
        foreach ($rawData as $key => $value) {
            $encrypted[$key] = match ($key) {
//                ERandselOrder::ID->value,
//                ERandselOrder::MAKER_ID->value,
//                ERandselOrder::MEMBER_ID->value,
//                ERandselOrder::PRODUCT_ID->value,
//                ERandselOrder::PRODUCT_NAME->value,
//                ERandselOrder::PRICE->value,
//                ERandselOrder::STATUS->value,
//                ERandselOrder::STATUS_MODIFIED->value,
//                ERandselOrder::APPROVAL_TYPE->value,
//                ERandselOrder::IS_CONFIRMED->value,
//                ERandselOrder::CONFIRMED->value,
                ERandselOrder::NAME1->value,
                ERandselOrder::NAME2->value,
                ERandselOrder::NAME1_HURIGANA->value,
                ERandselOrder::NAME2_HURIGANA->value,
                ERandselOrder::ZIP_CODE->value,
                ERandselOrder::TDFK_CD->value,
                ERandselOrder::ADDRESS1->value,
                ERandselOrder::ADDRESS2->value,
                ERandselOrder::ADDRESS3->value,
                ERandselOrder::TEL->value,
                ERandselOrder::EMAIL->value,
//                ERandselOrder::EMAIL_SEND_NG_FLG->value,
                ERandselOrder::SURVEY_JSON->value,
//                ERandselOrder::CREATED->value,
//                ERandselOrder::MODIFIED->value,
                => Encryptor::encrypt($value),
                default => $value,
            };
        }
        return $encrypted;
    }
}
